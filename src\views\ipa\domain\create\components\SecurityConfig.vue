<template>
    <div class="security-config">
        <ct-box class="ct-box-domain-create-wrapper">
            <cute-titled-block title="DDoS防护">
                <div v-for="itm in ddosList" :key="itm.value"></div>
            </cute-titled-block>
        </ct-box>
    </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";

export default {
    name: "SecurityConfig",
    props: {},
    data() {
        return {
            form: {
                selectedDdos: [],
            },
        };
    },
    methods: {
        // 预留校验方法
        async validateForm() {
            return { valid: true, data: this.formData };
        },
        getFormData() {
            return this.formData;
        },
    },
    computed: {
        iconStyle(item) {
            const img = item.icon;
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const imgSrc = require("./images/" + img);
            return {
                "background-image": `url(${imgSrc})`,
            };
        },
        ddosList() {
            return [
                { icon: "bot-bg-1.png", label: "DDoS防护-中国内地", disabled: false, value: 0 },
                { icon: "bot-bg-1.png", label: "DDoS防护-全球（不含中国内地）", disabled: false, value: 1 },
                { icon: "bot-bg-1.png", label: "DDoS防护超量处置策略）", disabled: false, value: 2 },
            ];
        },
    },
};
</script>

<style lang="scss" scoped>
.security-config {
    .placeholder {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #606266;
        font-size: 14px;
        padding: 12px 0;
    }
}
</style>
